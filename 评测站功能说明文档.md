# 评测站功能说明文档

> 本文档由PDF自动转换生成

---


## 第 1 页

### 评测站功能说明文档​
### 1. 页面模块概述​
根据最后一次沟通，站点主要分为：首页、产品、相册、活动、我的五大模块。
2. 首页​
2.1 顶部搜索区域​
- 搜索框：用户可以输入关键词搜索对应圣杯，支持产品名、品牌名、标签、商品编号等多种类型关
键字
- 搜索提示：搜索框内需要默认显示提示文字（placeholder），提示用户可以输入的关键字​
- 搜索按钮：点击后执行搜索操作，跳转至搜索结果页面。
- 搜素背景：需要后台可以自定义。
Note：简单的多关键词匹配可以通过数据库实现。如果追求类似2DF那样的极致搜索效果，需要上
ElasticSearch之类的搜索引擎，会提高对服务器硬件配置的要求（每月成本约增加200-300rmb）。如
果对成本比较敏感，可以考虑初期简单实现，后面再进行功能增强。
2.2 侧栏​
2.2.1 用户信息​

![图片 1](assets/images/image_001.png)


## 第 2 页

- 
用户头像：显示登录用户的个人头像。（头像后的背景图是用户自行设置？）
- 
用户昵称：显示用户的昵称
- 
个人统计数据：展示用户的关注数、粉丝数、获赞数等社交数据。
2.2.2 商品推荐区域​
### 功能目的：向用户推荐热门或优质的圣杯
具体内容：
- 
展示精选的手办商品图片，根据布局最多显示3条，不产生横向滚动条​
- 
显示商品价格信息
- 
预览图左上角需要显示购买渠道Label
- 
支持用户点击查看详细信息

![图片 2](assets/images/image_002.png)


![图片 3](assets/images/image_003.png)


## 第 3 页

- 
商品推荐区域所显示的商品，由管理后台人工设定，按权重值排序。点击 更多 按钮时，跳到商品
推荐列表页面（和商品列表共享同一个模板）
2.2.3 用户排行榜​
### 功能目的：展示平台上最活跃和最受欢迎的用户
排行维度：
- 
今日排行：当天最活跃的用户
- 
一周排行：近7天表现突出的用户 ​
- 
一月排行：近30天的优秀用户​
展示信息：
- 
用户头像和昵称
- 
前三名的排名数字为特殊图标（需要提供排名图标）
- 
用户等级标识
- 
积分或热度增加数值
Note：需要明确排行规则以及最右侧的数字显示规则。​
个人建议初期不要弄这个，因为没数据。或者不要弄这么多时间维度。

![图片 4](assets/images/image_004.png)


## 第 4 页

此处或许可以考虑把右侧的帖子推荐挪过来……或者改成每日评论排行之类的，还能有点激励玩家发
帖的作用。
2.3 内容分类导航​
导航选项：
- 
全部：显示所有类型的内容
- 
产品：显示最新添加的圣杯，后台可以进行权限设置，调整列表排序。
- 
相册：用户的相册动态（新增加了图片、评论等），后台可以隐藏特定动态和调整排序。
- 
活动：后台发布的活动贴。客户要求根据活动状态（进行中、已结束）进行样式区别显示。开展中
的为展开详情状态，已结束的自动收起。
- 
我的：点击后，进入用户面板页面。
2.3.1 动态内容展示区域​
每个动态卡片包含以下信息：
- 
用户信息：头像、昵称、用户等级
- 
发布时间：内容发布的时间（人类友好化）
- 
内容预览：文字描述和图片。
- 
封面图：根据动态类型不同，自动匹配。比如产品动态显示产品封面图、相册动态显示相册封面、
评论根据所评论资源显示对应图片。 页面​

![图片 5](assets/images/image_005.png)


## 第 5 页

3. 产品列表页面​
3.1 顶部筛选区域​
按产品录入时的标签分类，来设置对应的筛选分类。如果要支持多分类联合筛选，需要上
Elasticsearch 来实现。​
3.2 产品列表​

![图片 6](assets/images/image_006.png)


![图片 7](assets/images/image_007.png)
