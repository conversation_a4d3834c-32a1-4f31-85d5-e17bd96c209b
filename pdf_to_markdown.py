#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import fitz  # PyMuPDF
import os
import re
from PIL import Image
import io

def extract_pdf_content(pdf_path, output_dir=".", assets_dir="assets/images"):
    """
    提取PDF内容并转换为Markdown格式
    """
    # 确保assets目录存在
    os.makedirs(assets_dir, exist_ok=True)
    
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    
    markdown_content = []
    image_counter = 1
    
    print(f"正在处理PDF文件: {pdf_path}")
    print(f"总页数: {len(doc)}")
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        print(f"处理第 {page_num + 1} 页...")
        
        # 提取文本
        text = page.get_text()
        
        # 提取图片
        image_list = page.get_images()
        
        # 处理页面文本
        if text.strip():
            # 清理文本格式
            cleaned_text = clean_text(text)
            if cleaned_text.strip():
                markdown_content.append(f"\n## 第 {page_num + 1} 页\n")
                markdown_content.append(cleaned_text)
        
        # 处理页面图片
        for img_index, img in enumerate(image_list):
            try:
                # 获取图片数据
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                # 如果是CMYK格式，转换为RGB
                if pix.n - pix.alpha < 4:
                    img_data = pix.tobytes("png")
                else:
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    img_data = pix1.tobytes("png")
                    pix1 = None
                
                # 保存图片
                img_filename = f"image_{image_counter:03d}.png"
                img_path = os.path.join(assets_dir, img_filename)
                
                with open(img_path, "wb") as img_file:
                    img_file.write(img_data)
                
                # 在markdown中添加图片引用
                markdown_content.append(f"\n![图片 {image_counter}]({assets_dir}/{img_filename})\n")
                
                print(f"保存图片: {img_path}")
                image_counter += 1
                
                pix = None
                
            except Exception as e:
                print(f"处理图片时出错: {e}")
                continue
    
    doc.close()
    
    # 生成最终的markdown内容
    final_markdown = generate_markdown_header() + "\n".join(markdown_content)
    
    return final_markdown

def clean_text(text):
    """
    清理和格式化文本
    """
    # 移除多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)
    text = re.sub(r' +', ' ', text)
    
    # 处理标题（基于字体大小或位置的启发式方法）
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测可能的标题（简单的启发式方法）
        if len(line) < 50 and not line.endswith('.') and not line.endswith('。'):
            # 可能是标题
            if any(keyword in line for keyword in ['功能', '说明', '介绍', '概述', '系统', '模块']):
                cleaned_lines.append(f"### {line}")
            else:
                cleaned_lines.append(line)
        else:
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def generate_markdown_header():
    """
    生成Markdown文档头部
    """
    header = """# 评测站功能说明文档

> 本文档由PDF自动转换生成

---

"""
    return header

def main():
    pdf_file = "评测站功能说明文档.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: 找不到PDF文件 {pdf_file}")
        return
    
    try:
        # 提取PDF内容
        markdown_content = extract_pdf_content(pdf_file)
        
        # 保存Markdown文件
        output_file = "评测站功能说明文档.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"\n转换完成!")
        print(f"Markdown文件已保存为: {output_file}")
        print(f"图片已保存到: assets/images/ 目录")
        
    except Exception as e:
        print(f"转换过程中出现错误: {e}")

if __name__ == "__main__":
    main()
